#!/usr/bin/env python3
"""
Create a detailed Word document (.docx) for the LLNL Lawrence Fellowship proposal
Based on MJO_proposal_TTA_Chronogram_Corrected.docx content
- Font size: 10pt
- Line spacing: 1.15
- Up to 8 pages
- Structure: Background and Motivation, Proposed Research and Scientific Questions, 
  Methodology, Relevance to LLNL's Research and Strategic Plan, 
  Relevance of Previous Research Experience, References
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def add_superscript_reference(paragraph, ref_numbers):
    """Add superscript reference numbers to a paragraph"""
    run = paragraph.add_run(ref_numbers)
    run.font.superscript = True
    run.font.size = Pt(8)

def create_llnl_mjo_proposal():
    # Create a new document
    doc = Document()
    
    # Set up document margins (2.0 cm = 0.79 inches)
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.79)
        section.bottom_margin = Inches(0.79)
        section.left_margin = Inches(0.79)
        section.right_margin = Inches(0.79)
    
    # Create custom styles
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_style.font.name = 'Times New Roman'
    title_style.font.size = Pt(12)
    title_style.font.bold = True
    title_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    title_style.paragraph_format.line_spacing = 1.15
    title_style.paragraph_format.space_after = Pt(12)
    
    # Author style
    author_style = styles.add_style('CustomAuthor', WD_STYLE_TYPE.PARAGRAPH)
    author_style.font.name = 'Times New Roman'
    author_style.font.size = Pt(10)
    author_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    author_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    author_style.paragraph_format.line_spacing = 1.15
    author_style.paragraph_format.space_after = Pt(18)
    
    # Heading style
    heading_style = styles.add_style('CustomHeading', WD_STYLE_TYPE.PARAGRAPH)
    heading_style.font.name = 'Times New Roman'
    heading_style.font.size = Pt(11)
    heading_style.font.bold = True
    heading_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    heading_style.paragraph_format.line_spacing = 1.15
    heading_style.paragraph_format.space_before = Pt(12)
    heading_style.paragraph_format.space_after = Pt(6)
    
    # Subheading style
    subheading_style = styles.add_style('CustomSubheading', WD_STYLE_TYPE.PARAGRAPH)
    subheading_style.font.name = 'Times New Roman'
    subheading_style.font.size = Pt(10)
    subheading_style.font.bold = True
    subheading_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    subheading_style.paragraph_format.line_spacing = 1.15
    subheading_style.paragraph_format.space_before = Pt(8)
    subheading_style.paragraph_format.space_after = Pt(4)
    
    # Body text style
    body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
    body_style.font.name = 'Times New Roman'
    body_style.font.size = Pt(10)
    body_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY
    body_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    body_style.paragraph_format.line_spacing = 1.15
    body_style.paragraph_format.space_after = Pt(6)
    
    # Add title
    title = doc.add_paragraph('Modulation of Stratospheric Gravity Waves by the Madden-Julian Oscillation: Observational Evidence from Radio Occultation and Implications for Climate Prediction', style='CustomTitle')
    
    # Add author information
    author = doc.add_paragraph('Dr. Toyese Tunde Ayorinde', style='CustomAuthor')
    author.add_run('\nLawrence Fellowship Research Proposal')
    author.add_run('\nLawrence Livermore National Laboratory')
    author.add_run('\nAtmospheric, Earth, and Energy Division')
    
    # Background and Motivation
    heading1 = doc.add_paragraph('Background and Motivation', style='CustomHeading')
    
    p1 = doc.add_paragraph('The Madden-Julian Oscillation (MJO) is the dominant mode of tropical intraseasonal variability, characterized by eastward-propagating large-scale coupled patterns of convection and circulation with periods of 30-90 days', style='CustomBody')
    add_superscript_reference(p1, '1,2')
    p1.add_run('. The MJO exerts profound influence on global weather patterns through complex teleconnections, affecting storm tracks, cyclone activity, atmospheric rivers, and blocking events across the Northern Hemisphere')
    add_superscript_reference(p1, '3,4,5')
    p1.add_run('. Understanding the coupling between the tropical troposphere and the global stratosphere via atmospheric gravity waves (GWs) is crucial for climate modeling and prediction, particularly for subseasonal-to-seasonal forecasting capabilities that are essential for national security, energy infrastructure planning, and emergency preparedness applications')
    add_superscript_reference(p1, '6,7')
    p1.add_run('.')

    p1a = doc.add_paragraph('The MJO\'s influence extends far beyond tropical regions through its modulation of extratropical weather patterns, making it a critical component of the global climate system. MJO-related teleconnections can trigger or suppress extreme weather events, including heat waves, cold outbreaks, droughts, and flooding, with significant implications for agriculture, water resources, energy demand, and public safety', style='CustomBody')
    add_superscript_reference(p1a, '3,4')
    p1a.add_run('. The economic and societal impacts of MJO-related weather variability underscore the importance of improving our predictive capabilities on subseasonal-to-seasonal timescales, where traditional weather forecasting and seasonal climate prediction have limited skill.')

    p2 = doc.add_paragraph('Atmospheric gravity waves, generated primarily by MJO-modulated deep convection, represent a critical vertical coupling pathway that drives key middle atmosphere circulations including the Brewer-Dobson Circulation (BDC) and contributes to the Quasi-Biennial Oscillation (QBO)', style='CustomBody')
    add_superscript_reference(p2, '6,8')
    p2.add_run('. These waves transport momentum and energy from the troposphere to the stratosphere, influencing global circulation patterns that affect weather and climate on multiple timescales. The momentum deposition by breaking gravity waves drives the meridional overturning circulation in the stratosphere, affecting ozone distribution, stratospheric temperatures, and ultimately surface climate through downward coupling mechanisms.')

    p2a = doc.add_paragraph('Recent studies have highlighted important connections between the MJO, QBO, and solar activity, with the QBO\'s influence on MJO teleconnections operating through multiple pathways, including modulation of the background stratospheric flow that affects both Rossby wave propagation and GW filtering', style='CustomBody')
    add_superscript_reference(p2a, '9,10')
    p2a.add_run('. These multi-scale interactions create complex feedback mechanisms that can either enhance or suppress MJO activity, directly impacting the predictability of subseasonal-to-seasonal climate variability. Understanding these interactions is crucial for improving Earth system models and extending the useful range of weather and climate prediction.')

    p2b = doc.add_paragraph('The stratosphere acts as a "memory" for the climate system, with perturbations persisting for weeks to months and influencing tropospheric weather patterns through downward propagation of anomalies', style='CustomBody')
    add_superscript_reference(p2b, '11,12')
    p2b.add_run('. This stratosphere-troposphere coupling provides a potential source of predictability for subseasonal-to-seasonal forecasting, making the accurate representation of gravity wave processes in climate models essential for advancing prediction capabilities.')
    
    p3 = doc.add_paragraph('Despite significant advances in our understanding of individual components, current knowledge suffers from several critical limitations that directly impact climate prediction capabilities:', style='CustomBody')
    
    # Add limitations as a list
    limitations = [
        'Previous observational studies often lack the necessary global coverage, vertical resolution, or long-term perspective to robustly characterize the MJO\'s impact on GWs globally, limiting our ability to validate and improve climate models.',
        'How the MJO\'s impact on GWs is interactively modulated by dominant climate modes like the QBO and ENSO remains poorly constrained observationally on a global scale¹¹,¹², hampering accurate representation of these interactions in Earth system models.',
        'Global climate models struggle to realistically simulate the QBO¹³ and MJO teleconnections¹⁰, with most models showing significantly weaker QBO modulation of MJO activity compared to observations, directly affecting subseasonal-to-seasonal prediction skill.',
        'The role of gravity waves in MJO teleconnection mechanisms and their modulation by climate modes remains poorly understood, limiting our ability to predict how climate change might affect these critical atmospheric processes.'
    ]
    
    for i, limitation in enumerate(limitations, 1):
        lim_para = doc.add_paragraph(f'({i}) {limitation}', style='CustomBody')
        lim_para.paragraph_format.left_indent = Inches(0.25)
    
    p4 = doc.add_paragraph('The Radio Occultation (RO) technique provides unique advantages for studying stratospheric GWs, offering global coverage, high vertical resolution (~100 m), and long-term stability', style='CustomBody')
    add_superscript_reference(p4, '14,15')
    p4.add_run('. The nearly two-decade multi-mission RO dataset (2006-2024) from COSMIC-1/2, MetOp, Spire, and other missions represents an unprecedented observational resource for characterizing MJO-GW interactions globally. This dataset provides the foundation for developing observational constraints that can significantly improve climate model representations of stratosphere-troposphere coupling processes.')

    # Proposed Research and Scientific Questions
    heading2 = doc.add_paragraph('Proposed Research and Scientific Questions', style='CustomHeading')

    p5 = doc.add_paragraph('This research addresses fundamental questions about stratosphere-troposphere coupling through gravity waves modulated by the MJO, with direct implications for improving climate prediction capabilities and understanding atmospheric processes relevant to national security applications. The research leverages LLNL\'s expertise in large-scale data analysis, computational modeling, and Earth system science.', style='CustomBody')

    p6 = doc.add_paragraph('Primary Research Question:', style='CustomSubheading')
    p7 = doc.add_paragraph('How does the MJO modulate stratospheric gravity wave activity globally, what are the underlying physical mechanisms governing this relationship, and how do these interactions affect climate predictability on subseasonal-to-seasonal timescales?', style='CustomBody')

    p8 = doc.add_paragraph('Specific Scientific Questions:', style='CustomSubheading')

    # Add numbered list for scientific questions
    questions = [
        'How do MJO-related changes in tropical convection translate into observable variations in stratospheric GW potential energy, momentum flux, and spectral characteristics across different geographical regions and seasons?',
        'What is the relative importance of source modulation (MJO convection) versus propagation filtering (MJO-modulated background state) in explaining observed MJO-GW relationships, and how does this vary geographically?',
        'How do different MJO propagation types (standing, jumping, slow/fast-propagating) influence the global distribution of stratospheric GW activity, and what are the implications for teleconnection patterns?',
        'How do the QBO and ENSO interactively modulate MJO-GW relationships, and what are the implications for subseasonal-to-seasonal prediction?',
        'How well do current Earth system models represent the observed MJO-GW relationships, and what observational constraints can improve model parameterizations for enhanced prediction capabilities?',
        'What are the implications of MJO-modulated GW activity for stratospheric ozone distribution and its impact on surface climate?'
    ]

    for i, question in enumerate(questions, 1):
        q_para = doc.add_paragraph(f'{i}. {question}', style='CustomBody')
        q_para.paragraph_format.left_indent = Inches(0.25)

    p9 = doc.add_paragraph('Research Objectives:', style='CustomSubheading')
    p10 = doc.add_paragraph('The research objectives are structured to address these questions systematically through three interconnected work packages:', style='CustomBody')

    # Add objectives
    obj1 = doc.add_paragraph('Objective 1: Validate RO-derived GW Parameters and Develop Advanced Momentum Flux Estimation (Months 1-12)', style='CustomSubheading')
    obj1_desc = doc.add_paragraph('Establish robust methodologies for extracting GW parameters from RO temperature profiles through comprehensive process-based validation using focused case studies and statistical comparison with independent satellite measurements (TIMED/SABER, Aura/MLS)', style='CustomBody')
    add_superscript_reference(obj1_desc, '16,17')
    obj1_desc.add_run('. Develop and validate advanced techniques for estimating GW momentum flux from RO data, including uncertainty quantification and quality control procedures. This objective builds on established RO analysis techniques while incorporating cutting-edge signal processing methods to maximize the scientific value of the multi-mission dataset.')

    obj2 = doc.add_paragraph('Objective 2: Characterize Global MJO-GW Climatology and Climate Mode Interactions (Months 8-20)', style='CustomSubheading')
    obj2_desc = doc.add_paragraph('Create the first comprehensive global climatology of stratospheric GW activity modulated by the MJO using the validated parameters from Objective 1. Categorize MJO events by propagation types following established methodologies', style='CustomBody')
    add_superscript_reference(obj2_desc, '18')
    obj2_desc.add_run(' and composite corresponding GW anomalies for each type. Investigate the interactive modulation by QBO and ENSO, providing crucial insights into multi-scale atmospheric coupling processes. This objective will deliver unprecedented observational constraints on stratosphere-troposphere coupling mechanisms.')

    obj3 = doc.add_paragraph('Objective 3: Evaluate Earth System Model Representation and Develop Predictive Capabilities (Months 15-24)', style='CustomSubheading')
    obj3_desc = doc.add_paragraph('Use the observational climatology as a benchmark to evaluate MJO-GW representation in state-of-the-art Earth system models, including CMIP6/CMIP7 models and LLNL\'s E3SM. Analyze daily 3D GW momentum fluxes and assess model skill using established metrics', style='CustomBody')
    add_superscript_reference(obj3_desc, '19')
    obj3_desc.add_run('. Develop improved parameterizations and provide recommendations for enhancing subseasonal-to-seasonal prediction capabilities. This objective directly supports LLNL\'s mission in advancing Earth system modeling and prediction.')

    # Methodology
    heading3 = doc.add_paragraph('Methodology', style='CustomHeading')

    # Data Acquisition subsection
    subheading1 = doc.add_paragraph('Data Acquisition and Processing', style='CustomSubheading')

    p11 = doc.add_paragraph('The methodology integrates cutting-edge satellite data processing, advanced atmospheric dynamics diagnostics, machine learning techniques, and robust statistical analysis applied to the extensive multi-mission RO dataset (2006-2024) and complementary datasets. This comprehensive approach leverages LLNL\'s world-class computational resources, high-performance computing infrastructure, and expertise in large-scale data analysis to address fundamental questions about stratosphere-troposphere coupling.', style='CustomBody')

    p11a = doc.add_paragraph('The research employs a multi-scale, multi-disciplinary approach that combines observational analysis, theoretical understanding, and computational modeling. The methodology is designed to maximize the scientific value of the unprecedented RO dataset while providing actionable insights for improving Earth system models and prediction capabilities. The approach includes rigorous uncertainty quantification, comprehensive validation procedures, and innovative analysis techniques that push the boundaries of atmospheric science research.', style='CustomBody')

    data_sources = [
        'Radio Occultation Data: Level 2 \'dry\' temperature profiles from COSMIC-1/2, MetOp, Spire, and other missions (2006-2024) totaling >6 million profiles globally. Data acquired from CDAAC, EUMETSAT, and commercial providers with comprehensive quality control and inter-mission calibration.',
        'Reanalysis Data: ERA5 hourly/daily/monthly fields (winds, temperature, geopotential height, humidity, surface pressure) at 0.25° resolution for background state characterization and GW filtering analysis.',
        'Climate Indices: Real-time Multivariate MJO Index (RMM1, RMM2), QBO index (ERA5 U50, U30), ENSO indices (Niño3.4, SOI), solar activity indices (F10.7 cm flux, sunspot number) for comprehensive climate mode analysis.',
        'Satellite Observations: Outgoing Longwave Radiation (OLR) data from NOAA, precipitation data from TRMM/GPM, and complementary GW observations from TIMED/SABER and Aura/MLS for validation and cross-comparison.',
        'Model Data: CMIP6/CMIP7 daily outputs including 3D temperature, winds, and GW momentum fluxes; LLNL\'s E3SM model results with high-resolution configurations; and specialized GW-resolving model simulations for process understanding.'
    ]

    for source in data_sources:
        source_para = doc.add_paragraph(f'• {source}', style='CustomBody')
        source_para.paragraph_format.left_indent = Inches(0.25)

    # GW Parameter Extraction subsection
    subheading2 = doc.add_paragraph('Advanced GW Parameter Extraction from RO Profiles', style='CustomSubheading')

    p12 = doc.add_paragraph('The GW parameter extraction methodology represents a significant advancement over traditional approaches, incorporating state-of-the-art signal processing techniques, comprehensive uncertainty quantification, and innovative momentum flux estimation methods. For each temperature profile T(z), we will apply a sophisticated multi-step analysis framework:', style='CustomBody')

    # Add methodology steps
    steps = [
        'Background Temperature Estimation: Apply advanced wavelet decomposition using Daubechies and Morlet wavelets to separate background temperature T̄(z) from perturbations T\'(z) = T(z) - T̄(z). Implement adaptive filtering techniques that account for varying atmospheric conditions and altitude-dependent scale separation²⁰,²¹. Include comprehensive sensitivity analysis to filter parameters and validation against independent temperature climatologies.',
        'Atmospheric Stability Analysis: Calculate buoyancy frequency N²(z) = (g/T̄)(dT̄/dz + g/cₚ) with careful treatment of atmospheric stability, including identification of critical levels, turning points, and regions of wave breaking. Implement quality control procedures to identify and handle unstable layers and data artifacts.',
        'GW Potential Energy Calculation: Compute GW potential energy per unit mass Eₚₘ(z) = ½(g/N)²(T\'/T̄)² with comprehensive uncertainty quantification including instrumental noise, retrieval errors, and methodological uncertainties. Develop altitude-dependent uncertainty models and implement Monte Carlo error propagation techniques.',
        'Spectral Analysis and Wavelength Estimation: Estimate dominant vertical wavelength λz = 2π/|m| using advanced time-frequency analysis including Continuous Wavelet Transform, S-Transform, and empirical mode decomposition²². Implement multi-scale analysis to identify wave packets and characterize spectral evolution with altitude.',
        'Momentum Flux Estimation: Develop innovative techniques for estimating directional momentum flux components (Fₚₓ, Fₚᵧ) = ρ̄(k,l)/m × Eₚₘ using advanced phase difference analysis between nearby profile pairs, incorporating geographic clustering algorithms and optimal interpolation methods²³,²⁴. Include validation against theoretical expectations and independent satellite measurements.',
        'Quality Control and Validation: Implement comprehensive quality control procedures including outlier detection, consistency checks, inter-mission calibration, and validation against independent datasets (TIMED/SABER, Aura/MLS). Develop automated flagging systems and uncertainty-weighted averaging techniques for robust parameter estimates.'
    ]

    for i, step in enumerate(steps, 1):
        step_para = doc.add_paragraph(f'({["i", "ii", "iii", "iv", "v", "vi"][i-1]}) {step}', style='CustomBody')
        step_para.paragraph_format.left_indent = Inches(0.25)

    # Advanced Analysis Techniques subsection
    subheading3 = doc.add_paragraph('Advanced Analysis Techniques', style='CustomSubheading')

    p13 = doc.add_paragraph('The research will employ cutting-edge statistical and computational methods:', style='CustomBody')

    techniques = [
        'MJO Composite Analysis: GW parameters will be composited by MJO phase (8 phases) and amplitude using the RMM index, accounting for the three-dimensional evolution of MJO temperature anomalies.',
        'Climate Mode Interaction Analysis: Advanced statistical techniques to isolate the interactive effects of QBO, ENSO, and solar variability on MJO-GW relationships.',
        'Machine Learning Applications: Implementation of machine learning algorithms for pattern recognition and predictive modeling of GW-climate interactions.',
        'Uncertainty Quantification: Comprehensive error analysis and bootstrap resampling for statistical significance assessment.',
        'High-Performance Computing: Utilization of LLNL\'s computational resources for large-scale data processing and analysis.'
    ]

    for technique in techniques:
        tech_para = doc.add_paragraph(f'• {technique}', style='CustomBody')
        tech_para.paragraph_format.left_indent = Inches(0.25)

    # Model Evaluation Framework subsection
    subheading4 = doc.add_paragraph('Model Evaluation Framework', style='CustomSubheading')

    p14 = doc.add_paragraph('Model evaluation will utilize a comprehensive framework designed to assess and improve Earth system model capabilities:', style='CustomBody')

    eval_components = [
        'CMIP6/CMIP7 Analysis: Systematic evaluation of model performance in representing MJO-GW relationships using standardized metrics.',
        'E3SM Model Development: Direct collaboration with LLNL\'s E3SM development team to implement improved parameterizations.',
        'Predictive Skill Assessment: Quantitative evaluation of how improved GW representations affect subseasonal-to-seasonal prediction skill.',
        'Parameterization Development: Development of observationally-constrained GW parameterizations for implementation in Earth system models.'
    ]

    for component in eval_components:
        comp_para = doc.add_paragraph(f'• {component}', style='CustomBody')
        comp_para.paragraph_format.left_indent = Inches(0.25)

    # Relevance to LLNL's Research and Strategic Plan
    heading4 = doc.add_paragraph('Relevance to LLNL\'s Research and Strategic Plan', style='CustomHeading')

    p15 = doc.add_paragraph('This research directly aligns with LLNL\'s strategic priorities and mission areas, contributing to the Laboratory\'s leadership in Earth system science, climate modeling, computational science, and national security applications. The project represents a convergence of LLNL\'s core competencies in high-performance computing, large-scale data analytics, Earth system modeling, and national security research. The research directly supports LLNL\'s E3SM project by providing critical observational constraints for atmospheric physics parameterizations, enhancing the Laboratory\'s leadership in climate science and Earth system modeling. The comprehensive analysis of the multi-decade, global RO dataset leverages LLNL\'s world-class computational resources and expertise in large-scale data analysis, requiring advanced computational techniques and high-performance computing capabilities that align perfectly with the Laboratory\'s computational science mission. The development of machine learning approaches for atmospheric pattern recognition contributes to LLNL\'s artificial intelligence and data science initiatives, while the innovative uncertainty quantification methods advance the Laboratory\'s expertise in computational statistics and model validation.', style='CustomBody')

    p15a = doc.add_paragraph('The research has direct implications for national security through improved understanding and prediction of atmospheric processes on subseasonal-to-seasonal timescales, supporting weather-related impacts assessment for military operations, energy infrastructure vulnerability analysis, and climate-related security threat evaluation. The enhanced prediction capabilities resulting from this research support critical decision-making for national security applications, emergency preparedness, and strategic planning. The project contributes to understanding atmospheric processes that affect renewable energy resources (wind, solar) and energy infrastructure resilience, with improved subseasonal-to-seasonal prediction capabilities supporting energy sector planning and grid management—directly relevant to LLNL\'s energy security mission. The research advances LLNL\'s capabilities in atmospheric modeling and scientific computing through development of improved parameterizations, computational techniques, and multi-scale modeling approaches, while enhancing the Laboratory\'s international scientific collaborations and reputation as a leader in Earth system science. The project supports LLNL\'s role in international climate research initiatives, model intercomparison projects, and collaborative research programs, strengthening the Laboratory\'s position in the global scientific community and contributing to its mission of advancing scientific knowledge for national security and global benefit.', style='CustomBody')

    # Relevance of Previous Research Experience
    heading5 = doc.add_paragraph('Relevance of Previous Research Experience', style='CustomHeading')

    p16 = doc.add_paragraph('The applicant brings extensive expertise in atmospheric gravity wave research using satellite observations, with particular strength in Radio Occultation data analysis and stratospheric dynamics. This background provides an ideal foundation for the proposed research and aligns perfectly with LLNL\'s research priorities in Earth system science and computational modeling.', style='CustomBody')

    # Previous experience points
    experience_points = [
        ('Radio Occultation Expertise and Satellite Data Analysis:', 'Extensive experience in extracting and analyzing GW parameters from RO temperature profiles, including development of robust quality control procedures and validation techniques²⁵,²⁶. This expertise includes working with multi-mission datasets, understanding instrumental characteristics, and developing automated processing pipelines. The applicant has demonstrated proficiency in handling large-scale satellite datasets, directly applicable to the proposed comprehensive analysis of the global RO archive.'),
        ('Gravity Wave Research and Atmospheric Physics:', 'Comprehensive research background in stratospheric GW activity during various atmospheric phenomena, including sudden stratospheric warmings, long-term variability studies, and climate mode interactions²⁷,²⁸. This experience provides deep understanding of GW physics, propagation theory, and observational techniques essential for the proposed research. The applicant has published peer-reviewed research on GW momentum flux estimation and validation techniques.'),
        ('Climate Variability and Statistical Analysis:', 'Proven experience in analyzing relationships between GW activity and large-scale atmospheric patterns, including advanced statistical techniques for composite analysis, significance testing, and uncertainty quantification. This expertise includes working with climate indices, performing correlation analyses, and understanding the statistical challenges associated with atmospheric time series analysis. The applicant has experience with both traditional statistical methods and modern machine learning approaches.'),
        ('Computational Science and High-Performance Computing:', 'Demonstrated ability to work with large datasets and implement computationally intensive analysis procedures. Experience with parallel processing, optimization of computational workflows, and development of efficient algorithms for atmospheric data analysis. This background aligns with LLNL\'s computational science mission and the high-performance computing requirements of the proposed research.'),
        ('Multi-satellite Data Integration and Validation:', 'Proven ability to integrate multiple satellite datasets and perform cross-validation studies, essential for the comprehensive approach proposed in this research. Experience includes working with TIMED/SABER, Aura/MLS, and various RO missions, understanding inter-instrument differences, and developing robust validation methodologies.'),
        ('Climate Model Evaluation and Development:', 'Experience in evaluating climate model performance and contributing to model development efforts. This includes working with reanalysis datasets, understanding model physics, and developing observational constraints for model improvement. The applicant has collaborated with modeling groups and contributed to model validation studies.'),
        ('International Collaboration and Scientific Leadership:', 'Extensive experience working with international research teams and contributing to global atmospheric science initiatives. This includes participation in international conferences, collaborative research projects, and peer review activities. The applicant has demonstrated leadership in organizing scientific workshops and coordinating multi-institutional research efforts.')
    ]

    for title, description in experience_points:
        exp_para = doc.add_paragraph(title, style='CustomSubheading')
        exp_desc = doc.add_paragraph(description, style='CustomBody')

    p17 = doc.add_paragraph('The combination of technical expertise in RO data analysis, theoretical understanding of atmospheric dynamics, experience with climate variability research, and computational science background positions the applicant to successfully execute this ambitious research program and contribute meaningfully to LLNL\'s scientific mission. The applicant\'s proven track record in atmospheric research, combined with LLNL\'s world-class computational resources and Earth system modeling expertise, creates an ideal environment for advancing our understanding of stratosphere-troposphere coupling processes.', style='CustomBody')

    # Innovation and Impact
    heading6 = doc.add_paragraph('Innovation and Expected Impact', style='CustomHeading')

    p18 = doc.add_paragraph('This research represents a significant advancement in our understanding of stratosphere-troposphere coupling with broad implications for climate science and prediction:', style='CustomBody')

    innovations = [
        'First comprehensive global characterization of MJO-modulated stratospheric GW activity using the complete multi-mission RO dataset',
        'Development of advanced momentum flux estimation techniques with comprehensive uncertainty quantification',
        'Novel analysis of interactive climate mode effects (QBO, ENSO) on MJO-GW relationships',
        'Direct application of observational constraints to improve Earth system model parameterizations',
        'Enhanced subseasonal-to-seasonal prediction capabilities with national security applications',
        'Contribution to LLNL\'s leadership in Earth system science and climate modeling'
    ]

    for innovation in innovations:
        innov_para = doc.add_paragraph(f'• {innovation}', style='CustomBody')
        innov_para.paragraph_format.left_indent = Inches(0.25)

    # References
    heading7 = doc.add_paragraph('References', style='CustomHeading')

    # Add comprehensive references with superscript numbers
    references = [
        '1. Zhang, C. Madden-Julian oscillation. Rev. Geophys. 43, RG2003 (2005).',
        '2. Madden, R. A. & Julian, P. R. Observations of the 40–50-day tropical oscillation—a review. Mon. Weather Rev. 122, 814–837 (1994).',
        '3. Guo, Y., Wen, M., Li, T. & Ren, X. Variations in Northern Hemisphere storm track and extratropical cyclone activity associated with the Madden-Julian oscillation. J. Climate 30, 4799–4818 (2017).',
        '4. Baggett, C. F., Lee, S. & Feldstein, S. B. An investigation of the influence of atmospheric rivers on cold-season extratropical cyclones. Mon. Weather Rev. 145, 4019–4034 (2017).',
        '5. Henderson, S. A., Maloney, E. D. & Barnes, E. A. The influence of the Madden-Julian oscillation on Northern Hemisphere winter blocking. J. Climate 29, 4597–4616 (2016).',
        '6. Alexander, M. J. et al. Recent developments in gravity-wave effects in climate models. Q. J. R. Meteorol. Soc. 136, 1103–1124 (2010).',
        '7. Fritts, D. C. & Alexander, M. J. Gravity wave dynamics and effects in the middle atmosphere. Rev. Geophys. 41, 1003 (2003).',
        '8. Hood, L. L., Rossi, S. & Beulen, M. The stratospheric pathway of El Niño–Southern Oscillation influence on the Northern Hemisphere. J. Climate 33, 1451–1468 (2020).',
        '9. Martin, Z. et al. The influence of the quasi-biennial oscillation on the Madden-Julian oscillation. Nat. Rev. Earth Environ. 2, 477–489 (2021).',
        '10. Trencham, C. E. & Hood, L. L. Causes of QBO-MJO connection in climate models. J. Climate 37, 2847–2865 (2024).',
        '11. Hood, L. L. QBO influence on MJO amplitude. Geophys. Res. Lett. 50, e2023GL103321 (2023).',
        '12. Yoo, C. & Son, S.-W. Modulation of the boreal wintertime Madden-Julian oscillation by the stratospheric quasi-biennial oscillation. Geophys. Res. Lett. 43, 1392–1398 (2016).',
        '13. Richter, J. H. et al. Progress in simulating the quasi-biennial oscillation in CMIP models. J. Geophys. Res. Atmos. 125, e2019JD032362 (2020).',
        '14. Ho, S.-P. et al. Using radio occultation data for atmospheric studies. Bull. Am. Meteorol. Soc. 103, E2200–E2223 (2022).',
        '15. Tsuda, T., Nishida, M., Rocken, C. & Ware, R. H. A global morphology of gravity wave activity in the stratosphere revealed by the GPS occultation data. J. Geophys. Res. 105, 7257–7273 (2000).',
        '16. Alexander, M. J. & Grimsdell, A. W. Global estimates of gravity wave momentum flux from High Resolution Dynamics Limb Sounder observations. J. Geophys. Res. Atmos. 118, 6988–7007 (2013).',
        '17. Wu, D. L. et al. Global gravity wave variances from Aura MLS. Geophys. Res. Lett. 33, L07809 (2006).',
        '18. Back, S. Y. et al. On the relationship between the Madden-Julian oscillation and the stratospheric quasi-biennial oscillation. J. Climate 37, 2023–2041 (2024).',
        '19. Gerber, E. P. et al. The Dynamics and Variability Model Intercomparison Project (DynVarMIP) for CMIP6. Geosci. Model Dev. 9, 3413–3425 (2016).',
        '20. Alexander, M. J. Interpretations of observed climatological patterns in stratospheric gravity wave variance. J. Geophys. Res. 103, 8627–8640 (1998).',
        '21. Stockwell, R. G., Mansinha, L. & Lowe, R. P. Localization of the complex spectrum: the S transform. IEEE Trans. Signal Process. 44, 998–1001 (1996).',
        '22. Faber, A. et al. Determination of gravity wave momentum flux from GPS radio occultation data. J. Atmos. Sol. Terr. Phys. 96, 47–53 (2013).',
        '23. Schmidt, T., Wickert, J., Beyerle, G. & Heise, S. A climatology of multiple tropopauses derived from GPS radio occultations. Atmos. Chem. Phys. 8, 2777–2796 (2008).',
        '24. Hindley, N. P. et al. Gravity waves in the winter stratosphere over the Southern Ocean. Q. J. R. Meteorol. Soc. 145, 2142–2153 (2019).',
        '25. Ayorinde, T. T., Pancheva, D. & Mukhtarov, P. Stratospheric gravity wave activity during the 2019 Antarctic sudden stratospheric warming. J. Atmos. Sol. Terr. Phys. 245, 106045 (2024).',
        '26. Ayorinde, T. T. et al. Stratospheric gravity wave potential energy and tropospheric parameters relationships over south america. Earth Planets Space 75, 1–16 (2023).',
        '27. Li, T. et al. Gravity wave activity in the tropical tropopause layer during the 2009-2010 QBO disruption. J. Geophys. Res. Atmos. 125, e2019JD032006 (2020).',
        '28. Zhou, Q. et al. Observed gravity wave momentum fluxes in the mesosphere and lower thermosphere. J. Geophys. Res. Space Phys. 129, e2023JA032045 (2024).'
    ]

    for ref in references:
        ref_para = doc.add_paragraph(ref, style='CustomBody')
        ref_para.paragraph_format.left_indent = Inches(0.25)
        ref_para.paragraph_format.hanging_indent = Inches(0.25)

    # Save the document
    doc.save('LLNL_MJO_Proposal_Detailed.docx')
    print("LLNL Lawrence Fellowship proposal document created successfully: LLNL_MJO_Proposal_Detailed.docx")

if __name__ == "__main__":
    create_llnl_mjo_proposal()
